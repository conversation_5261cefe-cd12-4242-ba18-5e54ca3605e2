#!/usr/bin/env python3
"""
Activate service account for YouTube channel access.
This script attempts to activate the service account by making API calls
that should automatically accept any pending invitations.
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from youtube.uploader import YouTubeUploader


async def activate_service_account(service_account_file: str) -> bool:
    """
    Activate service account by attempting various YouTube API calls.
    This should automatically accept any pending invitations.
    """
    try:
        print("🔐 Initializing YouTube service with service account...")
        
        uploader = YouTubeUploader(service_account_file)
        auth_success = await uploader.authenticate()
        
        if not auth_success:
            print("❌ Failed to authenticate with YouTube API")
            return False
        
        print("✅ YouTube API authentication successful")
        
        # Try different API calls to activate the service account
        activation_attempts = [
            ("channels.list (mine=true)", lambda: uploader.youtube_service.channels().list(
                part='snippet,contentDetails,statistics',
                mine=True
            ).execute()),
            
            ("channels.list (managedByMe=true)", lambda: uploader.youtube_service.channels().list(
                part='snippet',
                managedByMe=True
            ).execute()),
            
            ("playlists.list", lambda: uploader.youtube_service.playlists().list(
                part='snippet',
                mine=True,
                maxResults=1
            ).execute()),
            
            ("videos.list (empty)", lambda: uploader.youtube_service.videos().list(
                part='snippet',
                id='nonexistent'  # This will return empty but tests permissions
            ).execute()),
        ]
        
        successful_calls = 0
        channel_info = None
        
        for call_name, api_call in activation_attempts:
            try:
                print(f"🔍 Attempting: {call_name}")
                response = api_call()
                
                if 'items' in response:
                    print(f"✅ {call_name} successful - Found {len(response['items'])} items")
                    
                    # If this is a channels call and we got results, save the channel info
                    if 'channels' in call_name and response['items']:
                        channel_info = response['items'][0]
                        print(f"📺 Channel found: {channel_info['snippet']['title']}")
                        print(f"🆔 Channel ID: {channel_info['id']}")
                        
                        # Check if we have upload permissions by looking at contentDetails
                        if 'contentDetails' in channel_info:
                            uploads_playlist = channel_info['contentDetails'].get('relatedPlaylists', {}).get('uploads')
                            if uploads_playlist:
                                print(f"📤 Uploads playlist ID: {uploads_playlist}")
                else:
                    print(f"✅ {call_name} successful - No items returned")
                
                successful_calls += 1
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ {call_name} failed: {error_msg}")
                
                # Check for specific error types
                if "insufficientPermissions" in error_msg:
                    print("   → This indicates the service account needs more permissions")
                elif "youtubeSignupRequired" in error_msg:
                    print("   → This indicates the service account is not associated with a YouTube channel")
                elif "forbidden" in error_msg.lower():
                    print("   → This indicates access is forbidden - check channel permissions")
                elif "quotaExceeded" in error_msg:
                    print("   → API quota exceeded - this is actually a good sign for permissions!")
                    successful_calls += 1  # Count quota errors as success for permissions
        
        print(f"\n📊 Summary: {successful_calls}/{len(activation_attempts)} API calls successful")
        
        if successful_calls > 0:
            print("✅ Service account appears to be activated!")
            
            if channel_info:
                print(f"\n📺 Channel Details:")
                print(f"   Name: {channel_info['snippet']['title']}")
                print(f"   ID: {channel_info['id']}")
                print(f"   Description: {channel_info['snippet'].get('description', 'No description')[:100]}...")
                
                # Check statistics if available
                if 'statistics' in channel_info:
                    stats = channel_info['statistics']
                    print(f"   Subscribers: {stats.get('subscriberCount', 'Hidden')}")
                    print(f"   Videos: {stats.get('videoCount', '0')}")
                    print(f"   Views: {stats.get('viewCount', '0')}")
            
            return True
        else:
            print("❌ Service account activation failed")
            print("\nPossible issues:")
            print("1. Service account invitation hasn't been sent yet")
            print("2. Service account invitation was sent but hasn't taken effect yet (wait 5-10 minutes)")
            print("3. Service account was given insufficient permissions (needs Editor or Manager role)")
            print("4. YouTube Data API v3 is not enabled for the Google Cloud project")
            
            return False
            
    except Exception as e:
        print(f"❌ Activation failed with error: {e}")
        return False


async def test_upload_permissions(service_account_file: str) -> bool:
    """Test if the service account can perform upload-related operations."""
    try:
        print("\n🧪 Testing upload permissions...")
        
        uploader = YouTubeUploader(service_account_file)
        await uploader.authenticate()
        
        # Test video categories (this requires upload permissions)
        try:
            categories = uploader.youtube_service.videoCategories().list(
                part='snippet',
                regionCode='US'
            ).execute()
            
            print(f"✅ Can access video categories: {len(categories.get('items', []))} categories found")
            return True
            
        except Exception as e:
            print(f"❌ Cannot access video categories: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Upload permission test failed: {e}")
        return False


def print_next_steps(success: bool, service_account_email: str):
    """Print next steps based on activation result."""
    print("\n" + "="*60)

    if success:
        print("🎉 SERVICE ACCOUNT ACTIVATION SUCCESSFUL!")
        print("="*60)
        print()
        print("Your service account is now activated and ready to use!")
        print()
        print("Next steps:")
        print("1. Test the upload functionality:")
        print("   python lofi upload videos/my_video.mp4 chill")
        print()
        print("2. Check the pipeline status:")
        print("   python lofi status")
        print()
        print("3. Generate and upload a new video:")
        print("   python lofi generate 180 chill -o test_video.mp4")
        print("   python lofi upload test_video.mp4 chill")

    else:
        print("❌ SERVICE ACCOUNT ACTIVATION FAILED")
        print("="*60)
        print()
        print("The service account still needs to be properly configured.")
        print()
        print("🔧 STEP-BY-STEP SOLUTION:")
        print()
        print("1. 📺 Open YouTube Studio:")
        print("   https://studio.youtube.com/")
        print()
        print("2. ⚙️ Navigate to Settings:")
        print("   - Click 'Settings' (gear icon) in the left sidebar")
        print("   - Click 'Permissions' tab")
        print()
        print("3. ➕ Add the service account:")
        print("   - Click the 'Invite' button")
        print(f"   - Enter this email: {service_account_email}")
        print("   - Select 'Editor' role (recommended) or 'Manager'")
        print("   - Click 'Done'")
        print()
        print("4. ✅ The invitation should be automatically accepted")
        print("   (Service accounts don't need manual email acceptance)")
        print()
        print("5. ⏰ Wait 2-5 minutes for permissions to propagate")
        print()
        print("6. 🧪 Test again:")
        print("   python scripts/activate_service_account.py")
        print()
        print("🔍 TROUBLESHOOTING:")
        print()
        print("If it still doesn't work:")
        print("- Check Google Cloud Console:")
        print("  https://console.cloud.google.com/apis/dashboard")
        print("- Ensure YouTube Data API v3 is enabled")
        print("- Verify the service account exists and has the right permissions")
        print("- Try removing and re-adding the service account to your channel")
        print()
        print("📧 Service account email to copy:")
        print(f"   {service_account_email}")


def open_youtube_studio():
    """Open YouTube Studio in the browser."""
    import webbrowser
    try:
        webbrowser.open("https://studio.youtube.com/")
        print("🌐 Opening YouTube Studio in your browser...")
        return True
    except Exception as e:
        print(f"❌ Could not open browser: {e}")
        print("Please manually go to: https://studio.youtube.com/")
        return False


async def main():
    """Main activation function."""
    print("🚀 Service Account Activation for YouTube")
    print("="*40)

    # Load environment
    from dotenv import load_dotenv
    load_dotenv()

    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")

    if not service_account_file:
        print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE environment variable not set")
        print("Please set it in your .env file:")
        print("YOUTUBE_SERVICE_ACCOUNT_FILE=/path/to/service-account.json")
        return False

    if not Path(service_account_file).exists():
        print(f"❌ Service account file not found: {service_account_file}")
        return False

    # Get service account email for instructions
    with open(service_account_file, 'r') as f:
        data = json.load(f)
        service_account_email = data.get('client_email')

    print(f"📁 Service account file: {service_account_file}")
    print(f"📧 Service account email: {service_account_email}")
    print()

    # Attempt activation
    success = await activate_service_account(service_account_file)

    if success:
        # Test upload permissions
        upload_success = await test_upload_permissions(service_account_file)
        success = success and upload_success
    else:
        # If activation failed, offer to open YouTube Studio
        print("\n🌐 Would you like me to open YouTube Studio for you? (y/n): ", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                open_youtube_studio()
                print()
                print("📋 Copy this service account email:")
                print(f"   {service_account_email}")
                print()
                print("⏰ After adding the service account, wait 2-5 minutes and run:")
                print("   python scripts/activate_service_account.py")
        except (EOFError, KeyboardInterrupt):
            print("\nSkipping browser opening.")

    # Print next steps
    print_next_steps(success, service_account_email)

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
