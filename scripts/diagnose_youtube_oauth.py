#!/usr/bin/env python3
"""
Diagnose YouTube OAuth2 authentication issues.
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from youtube.uploader import YouTubeUploader


def check_oauth_credentials_file(file_path: str) -> bool:
    """Check if OAuth2 credentials file is valid."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ OAuth2 credentials file not found: {file_path}")
            return False
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        # Check for OAuth2 structure
        if 'installed' not in data and 'web' not in data:
            print(f"❌ Invalid OAuth2 credentials format")
            return False
        
        oauth_data = data.get('installed', data.get('web', {}))
        required_fields = ['client_id', 'client_secret', 'auth_uri', 'token_uri']
        missing_fields = [field for field in required_fields if field not in oauth_data]
        
        if missing_fields:
            print(f"❌ Missing required OAuth2 fields: {', '.join(missing_fields)}")
            return False
        
        print(f"✅ OAuth2 credentials file is valid")
        print(f"   🆔 Client ID: {oauth_data.get('client_id')[:20]}...")
        print(f"   🏗️ Project ID: {oauth_data.get('project_id', 'Not specified')}")
        return True
        
    except Exception as e:
        print(f"❌ Error reading OAuth2 credentials file: {e}")
        return False


async def test_oauth_authentication(oauth_credentials_file: str) -> bool:
    """Test OAuth2 authentication."""
    try:
        print("\n🔐 Testing OAuth2 authentication...")
        
        uploader = YouTubeUploader(oauth_credentials_file, auth_type="oauth2")
        
        # Check if token already exists
        auth_info = uploader.get_auth_info()
        if auth_info.get("token_exists"):
            print("📄 Existing OAuth2 token found")
        else:
            print("📝 No existing token - will need to authenticate")
        
        auth_success = await uploader.authenticate()
        
        if auth_success:
            print("✅ OAuth2 authentication successful")
            
            # Try to access channel information
            try:
                response = uploader.youtube_service.channels().list(
                    part='snippet,contentDetails,statistics',
                    mine=True
                ).execute()
                
                if response.get('items'):
                    channel = response['items'][0]
                    print(f"✅ Successfully accessed YouTube channel:")
                    print(f"   📺 Channel: {channel['snippet']['title']}")
                    print(f"   🆔 Channel ID: {channel['id']}")
                    
                    # Check upload permissions
                    if 'contentDetails' in channel:
                        uploads_playlist = channel['contentDetails'].get('relatedPlaylists', {}).get('uploads')
                        if uploads_playlist:
                            print(f"   📤 Uploads playlist: {uploads_playlist}")
                    
                    return True
                else:
                    print("❌ No YouTube channel found")
                    print("   Make sure you have a YouTube channel associated with your Google account")
                    return False
                    
            except Exception as e:
                print(f"❌ Failed to access YouTube channel: {e}")
                return False
        else:
            print("❌ OAuth2 authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False


def print_troubleshooting_steps():
    """Print troubleshooting steps."""
    print("\n" + "="*60)
    print("🔧 TROUBLESHOOTING STEPS")
    print("="*60)
    print()
    print("If authentication is failing, try these steps:")
    print()
    print("1. 🔄 Clear existing token:")
    print("   - Delete youtube_token.json if it exists")
    print("   - Run this script again")
    print()
    print("2. ✅ Verify OAuth2 setup:")
    print("   - Make sure YouTube Data API v3 is enabled in Google Cloud Console")
    print("   - Verify OAuth2 client is configured correctly")
    print("   - Check that redirect URIs include 'http://localhost'")
    print()
    print("3. 🌐 Check browser:")
    print("   - Make sure you can access the authentication URL")
    print("   - Complete the OAuth2 flow in the browser")
    print("   - Grant all requested permissions")
    print()
    print("4. 📺 Verify YouTube channel:")
    print("   - Make sure you have a YouTube channel")
    print("   - Sign in with the correct Google account")
    print()
    print("5. 🔑 Re-create OAuth2 credentials if needed:")
    print("   - Go to Google Cloud Console > APIs & Services > Credentials")
    print("   - Delete old OAuth2 client and create a new one")


async def test_upload_permissions(oauth_credentials_file: str) -> bool:
    """Test upload permissions."""
    try:
        print("\n🧪 Testing upload permissions...")
        
        uploader = YouTubeUploader(oauth_credentials_file, auth_type="oauth2")
        await uploader.authenticate()
        
        # Test video categories access (required for uploads)
        try:
            categories = uploader.youtube_service.videoCategories().list(
                part='snippet',
                regionCode='US'
            ).execute()
            
            if categories.get('items'):
                print(f"✅ Can access video categories: {len(categories['items'])} categories")
                return True
            else:
                print("❌ Cannot access video categories")
                return False
                
        except Exception as e:
            print(f"❌ Upload permission test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Upload permission test failed: {e}")
        return False


async def main():
    """Main diagnostic function."""
    print("🔍 YouTube OAuth2 Authentication Diagnostics")
    print("="*40)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    oauth_credentials_file = os.getenv("YOUTUBE_OAUTH_CREDENTIALS_FILE")
    
    if not oauth_credentials_file:
        print("❌ YOUTUBE_OAUTH_CREDENTIALS_FILE environment variable not set")
        print("Please set it in your .env file:")
        print("YOUTUBE_OAUTH_CREDENTIALS_FILE=/path/to/oauth-credentials.json")
        return False
    
    print(f"📁 OAuth2 credentials file: {oauth_credentials_file}")
    
    # Check credentials file
    if not check_oauth_credentials_file(oauth_credentials_file):
        return False
    
    # Test authentication
    auth_success = await test_oauth_authentication(oauth_credentials_file)
    
    if auth_success:
        # Test upload permissions
        upload_success = await test_upload_permissions(oauth_credentials_file)
        
        if upload_success:
            print("\n✅ All checks passed! OAuth2 authentication is working correctly.")
            print("\nNext steps:")
            print("1. Test upload: python lofi upload videos/my_video.mp4 chill")
            print("2. Check status: python lofi status")
            return True
        else:
            print("\n⚠️ Authentication works but upload permissions may be limited")
    
    print_troubleshooting_steps()
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
