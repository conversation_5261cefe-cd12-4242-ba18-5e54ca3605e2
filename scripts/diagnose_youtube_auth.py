#!/usr/bin/env python3
"""
Diagnose YouTube authentication issues.
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from youtube.uploader import YouTubeUploader


def check_service_account_file(file_path: str) -> bool:
    """Check if service account file is valid."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ Service account file not found: {file_path}")
            return False
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return False
        
        if data.get('type') != 'service_account':
            print(f"❌ Invalid credential type: {data.get('type')} (expected: service_account)")
            return False
        
        print(f"✅ Service account file is valid")
        print(f"   📧 Service account email: {data.get('client_email')}")
        print(f"   🏗️ Project ID: {data.get('project_id')}")
        return True
        
    except Exception as e:
        print(f"❌ Error reading service account file: {e}")
        return False


async def test_youtube_authentication(service_account_file: str) -> bool:
    """Test YouTube API authentication."""
    try:
        print("\n🔐 Testing YouTube API authentication...")
        
        uploader = YouTubeUploader(service_account_file)
        auth_success = await uploader.authenticate()
        
        if auth_success:
            print("✅ YouTube API authentication successful")
            
            # Try to make a simple API call to test permissions
            try:
                # Test with a simple channels.list call
                response = uploader.youtube_service.channels().list(
                    part='snippet',
                    mine=True
                ).execute()
                
                if response.get('items'):
                    channel = response['items'][0]
                    print(f"✅ Successfully accessed YouTube channel:")
                    print(f"   📺 Channel: {channel['snippet']['title']}")
                    print(f"   🆔 Channel ID: {channel['id']}")
                    return True
                else:
                    print("❌ No YouTube channel found for this service account")
                    print("   This means the service account hasn't been added to any YouTube channel")
                    return False
                    
            except Exception as e:
                print(f"❌ Failed to access YouTube channel: {e}")
                if "youtubeSignupRequired" in str(e):
                    print("   This error means the service account needs to be added to your YouTube channel")
                elif "forbidden" in str(e).lower():
                    print("   This error means the service account doesn't have proper permissions")
                return False
        else:
            print("❌ YouTube API authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False


def print_fix_instructions(service_account_email: str):
    """Print instructions to fix the authentication issue."""
    print("\n" + "="*60)
    print("🔧 HOW TO FIX THE AUTHENTICATION ISSUE")
    print("="*60)
    print()
    print("The service account needs to be added to your YouTube channel.")
    print("Follow these steps:")
    print()
    print("1. 📺 Go to YouTube Studio:")
    print("   https://studio.youtube.com/")
    print()
    print("2. ⚙️ Navigate to Settings:")
    print("   - Click on 'Settings' in the left sidebar")
    print("   - Click on 'Permissions' tab")
    print()
    print("3. ➕ Add the service account:")
    print("   - Click 'Invite' button")
    print(f"   - Enter this email: {service_account_email}")
    print("   - Select 'Editor' or 'Manager' role")
    print("   - Click 'Done'")
    print()
    print("4. ✉️ Accept the invitation:")
    print("   - The service account will receive an email invitation")
    print("   - You need to accept it (this might require manual intervention)")
    print()
    print("5. 🧪 Test again:")
    print("   python scripts/diagnose_youtube_auth.py")
    print()
    print("📝 Note: It may take a few minutes for permissions to take effect.")


async def main():
    """Main diagnostic function."""
    print("🔍 YouTube Authentication Diagnostics")
    print("="*40)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    
    if not service_account_file:
        print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE environment variable not set")
        print("Please set it in your .env file:")
        print("YOUTUBE_SERVICE_ACCOUNT_FILE=/path/to/service-account.json")
        return False
    
    print(f"📁 Service account file: {service_account_file}")
    
    # Check service account file
    if not check_service_account_file(service_account_file):
        return False
    
    # Get service account email for instructions
    with open(service_account_file, 'r') as f:
        data = json.load(f)
        service_account_email = data.get('client_email')
    
    # Test authentication
    auth_success = await test_youtube_authentication(service_account_file)
    
    if not auth_success:
        print_fix_instructions(service_account_email)
        return False
    
    print("\n✅ All checks passed! YouTube authentication is working correctly.")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
