#!/usr/bin/env python3
"""
Force service account activation by trying different YouTube API approaches.
This script attempts to "wake up" the service account permissions by making
various API calls that might trigger the invitation acceptance.
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from google.auth.transport.requests import Request
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


class ServiceAccountActivator:
    """Handles service account activation attempts."""
    
    def __init__(self, service_account_file: str):
        self.service_account_file = Path(service_account_file)
        self.youtube_service = None
        self.service_account_email = None
        
    async def initialize(self) -> bool:
        """Initialize the YouTube service."""
        try:
            # Load service account info
            with open(self.service_account_file, 'r') as f:
                data = json.load(f)
                self.service_account_email = data.get('client_email')
            
            # Load credentials
            credentials = service_account.Credentials.from_service_account_file(
                str(self.service_account_file),
                scopes=['https://www.googleapis.com/auth/youtube.upload']
            )
            
            # Build service
            self.youtube_service = build('youtube', 'v3', credentials=credentials)
            print("✅ YouTube service initialized")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize YouTube service: {e}")
            return False
    
    async def try_activation_methods(self) -> bool:
        """Try various methods to activate the service account."""
        print("\n🔄 Attempting service account activation...")
        
        activation_methods = [
            ("Basic API test", self._test_basic_api),
            ("Channel access attempt", self._test_channel_access),
            ("Video categories access", self._test_video_categories),
            ("Search API test", self._test_search_api),
            ("Quota usage test", self._test_quota_usage),
        ]
        
        successful_methods = 0
        
        for method_name, method_func in activation_methods:
            try:
                print(f"🔍 Trying: {method_name}")
                success = await method_func()
                if success:
                    print(f"✅ {method_name} - SUCCESS")
                    successful_methods += 1
                else:
                    print(f"❌ {method_name} - FAILED")
                    
            except Exception as e:
                print(f"❌ {method_name} - ERROR: {e}")
        
        print(f"\n📊 Activation summary: {successful_methods}/{len(activation_methods)} methods successful")
        
        # If any method succeeded, the service account might be working
        return successful_methods > 0
    
    async def _test_basic_api(self) -> bool:
        """Test basic API connectivity."""
        try:
            # This should work even without channel permissions
            response = self.youtube_service.videoCategories().list(
                part='snippet',
                regionCode='US'
            ).execute()
            return len(response.get('items', [])) > 0
        except Exception:
            return False
    
    async def _test_channel_access(self) -> bool:
        """Test channel access."""
        try:
            response = self.youtube_service.channels().list(
                part='snippet',
                mine=True
            ).execute()
            return len(response.get('items', [])) > 0
        except Exception:
            return False
    
    async def _test_video_categories(self) -> bool:
        """Test video categories access."""
        try:
            response = self.youtube_service.videoCategories().list(
                part='snippet',
                regionCode='US'
            ).execute()
            return len(response.get('items', [])) > 0
        except Exception:
            return False
    
    async def _test_search_api(self) -> bool:
        """Test search API."""
        try:
            response = self.youtube_service.search().list(
                part='snippet',
                q='test',
                maxResults=1,
                type='video'
            ).execute()
            return 'items' in response
        except Exception:
            return False
    
    async def _test_quota_usage(self) -> bool:
        """Test quota usage by making a simple call."""
        try:
            # This call uses minimal quota
            response = self.youtube_service.i18nLanguages().list(
                part='snippet'
            ).execute()
            return len(response.get('items', [])) > 0
        except Exception:
            return False
    
    async def test_upload_simulation(self) -> bool:
        """Simulate an upload without actually uploading."""
        print("\n🧪 Testing upload simulation...")
        
        try:
            # Test if we can access upload-related endpoints
            # This doesn't upload anything but tests permissions
            
            # Test 1: Can we access video categories?
            categories = self.youtube_service.videoCategories().list(
                part='snippet',
                regionCode='US'
            ).execute()
            
            if not categories.get('items'):
                print("❌ Cannot access video categories")
                return False
            
            print("✅ Can access video categories")
            
            # Test 2: Can we access channel info?
            try:
                channels = self.youtube_service.channels().list(
                    part='snippet',
                    mine=True
                ).execute()
                
                if channels.get('items'):
                    channel = channels['items'][0]
                    print(f"✅ Can access channel: {channel['snippet']['title']}")
                    return True
                else:
                    print("❌ No channel access - service account not added to channel")
                    return False
                    
            except HttpError as e:
                if "insufficientPermissions" in str(e):
                    print("❌ Insufficient permissions - service account not properly added")
                    return False
                else:
                    print(f"❌ Channel access error: {e}")
                    return False
            
        except Exception as e:
            print(f"❌ Upload simulation failed: {e}")
            return False


def print_manual_steps(service_account_email: str):
    """Print manual steps for the user."""
    print("\n" + "="*70)
    print("📋 MANUAL ACTIVATION STEPS")
    print("="*70)
    print()
    print("Since automatic activation didn't work, please follow these steps:")
    print()
    print("1. 🌐 Open YouTube Studio:")
    print("   https://studio.youtube.com/")
    print()
    print("2. 🔧 Go to Settings:")
    print("   - Click the gear icon (Settings) in the left sidebar")
    print("   - Click the 'Permissions' tab")
    print()
    print("3. ➕ Add service account:")
    print("   - Click the 'Invite' button")
    print("   - Copy and paste this email:")
    print(f"     {service_account_email}")
    print("   - Select 'Editor' role")
    print("   - Click 'Done'")
    print()
    print("4. ⏰ Wait 2-5 minutes for changes to take effect")
    print()
    print("5. 🧪 Test again:")
    print("   python scripts/force_service_account_activation.py")
    print()
    print("💡 TIP: Service accounts don't need to 'accept' email invitations")
    print("   like regular users do. The permission should be immediate.")


async def main():
    """Main function."""
    print("🚀 Force Service Account Activation")
    print("="*40)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    
    if not service_account_file:
        print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE not set")
        return False
    
    if not Path(service_account_file).exists():
        print(f"❌ Service account file not found: {service_account_file}")
        return False
    
    # Initialize activator
    activator = ServiceAccountActivator(service_account_file)
    
    if not await activator.initialize():
        return False
    
    print(f"📧 Service account: {activator.service_account_email}")
    
    # Try activation methods
    activation_success = await activator.try_activation_methods()
    
    if activation_success:
        # Test upload simulation
        upload_success = await activator.test_upload_simulation()
        
        if upload_success:
            print("\n🎉 SUCCESS! Service account is activated and ready!")
            print("\nNext steps:")
            print("1. Test upload: python lofi upload videos/my_video.mp4 chill")
            print("2. Check status: python lofi status")
            return True
        else:
            print("\n⚠️ Partial success - API works but no channel access")
    
    # If we get here, activation failed
    print("\n❌ Automatic activation failed")
    print_manual_steps(activator.service_account_email)
    
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
