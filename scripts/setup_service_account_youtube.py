#!/usr/bin/env python3
"""
Complete guide to set up service account for YouTube uploads.
"""

import os
import sys
import json
import webbrowser
from pathlib import Path

def print_header():
    """Print header."""
    print("🔧 YouTube Service Account Setup Guide")
    print("="*50)
    print()

def check_service_account_file(file_path: str) -> dict:
    """Check and load service account file."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ Service account file not found: {file_path}")
            return None
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return None
        
        if data.get('type') != 'service_account':
            print(f"❌ Invalid credential type: {data.get('type')} (expected: service_account)")
            return None
        
        print(f"✅ Service account file is valid")
        print(f"   📧 Service account email: {data.get('client_email')}")
        print(f"   🏗️ Project ID: {data.get('project_id')}")
        return data
        
    except Exception as e:
        print(f"❌ Error reading service account file: {e}")
        return None

def print_youtube_studio_instructions(service_account_email: str):
    """Print detailed YouTube Studio setup instructions."""
    print("\n" + "="*70)
    print("📺 YOUTUBE STUDIO SETUP INSTRUCTIONS")
    print("="*70)
    print()
    print("Follow these steps EXACTLY:")
    print()
    print("1. 🌐 Open YouTube Studio:")
    print("   https://studio.youtube.com/")
    print()
    print("2. 🔧 Navigate to Settings:")
    print("   - Look for the gear icon (⚙️) in the left sidebar")
    print("   - Click on 'Settings'")
    print("   - Click on the 'Permissions' tab at the top")
    print()
    print("3. ➕ Add the service account:")
    print("   - Click the blue 'Invite' button")
    print("   - In the email field, copy and paste this EXACT email:")
    print(f"     {service_account_email}")
    print("   - Select 'Editor' from the role dropdown")
    print("   - Click 'Done'")
    print()
    print("4. ✅ Verify the invitation:")
    print("   - You should see the service account email in the permissions list")
    print("   - The role should show as 'Editor'")
    print()
    print("5. ⏰ Wait 2-3 minutes for changes to take effect")
    print()
    print("6. 🧪 Test the setup:")
    print("   python scripts/test_service_account.py")
    print()
    print("💡 IMPORTANT NOTES:")
    print("- Service accounts don't receive email invitations")
    print("- The permission should be active immediately after adding")
    print("- Make sure you're signed into the correct YouTube channel")
    print("- The service account email must be copied exactly (no spaces)")

def open_youtube_studio():
    """Try to open YouTube Studio."""
    try:
        webbrowser.open("https://studio.youtube.com/")
        print("🌐 Opening YouTube Studio in your browser...")
        return True
    except Exception as e:
        print(f"❌ Could not open browser: {e}")
        print("Please manually go to: https://studio.youtube.com/")
        return False

def print_troubleshooting():
    """Print troubleshooting steps."""
    print("\n" + "="*70)
    print("🔍 TROUBLESHOOTING")
    print("="*70)
    print()
    print("If you're still getting permission errors:")
    print()
    print("1. 🔄 Double-check the email:")
    print("   - Make sure there are no extra spaces")
    print("   - Copy the email exactly as shown above")
    print()
    print("2. 🎯 Verify the role:")
    print("   - Make sure you selected 'Editor' (not Viewer)")
    print("   - Editor role is required for uploading videos")
    print()
    print("3. ⏰ Wait longer:")
    print("   - Sometimes it takes up to 10 minutes for permissions to propagate")
    print("   - Try waiting and testing again")
    print()
    print("4. 🔄 Remove and re-add:")
    print("   - Remove the service account from YouTube Studio")
    print("   - Wait 1 minute")
    print("   - Add it again with Editor role")
    print()
    print("5. 🌐 Check Google Cloud Console:")
    print("   - Go to: https://console.cloud.google.com/")
    print("   - Navigate to 'APIs & Services' > 'Enabled APIs'")
    print("   - Make sure 'YouTube Data API v3' is enabled")
    print()
    print("6. 📺 Verify YouTube channel:")
    print("   - Make sure you're adding the service account to the correct channel")
    print("   - You must be the owner/manager of the YouTube channel")

def create_test_script():
    """Create a simple test script."""
    test_script_content = '''#!/usr/bin/env python3
"""
Test service account YouTube access.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from youtube.uploader import YouTubeUploader

async def test_service_account():
    """Test service account access."""
    from dotenv import load_dotenv
    load_dotenv()
    
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    if not service_account_file:
        print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE not set in .env")
        return False
    
    print("🧪 Testing service account access...")
    print(f"📁 Using: {service_account_file}")
    
    try:
        uploader = YouTubeUploader(service_account_file, auth_type="service_account")
        success = await uploader.authenticate()
        
        if success:
            print("✅ Authentication successful!")
            
            # Test channel access
            response = uploader.youtube_service.channels().list(
                part='snippet',
                mine=True
            ).execute()
            
            if response.get('items'):
                channel = response['items'][0]
                print(f"✅ Channel access successful!")
                print(f"   📺 Channel: {channel['snippet']['title']}")
                print(f"   🆔 Channel ID: {channel['id']}")
                return True
            else:
                print("❌ No channel access - service account not added to channel")
                return False
        else:
            print("❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        if "insufficientPermissions" in str(e):
            print("   → Service account needs to be added to YouTube channel")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_service_account())
    if success:
        print("\\n🎉 Service account is working correctly!")
        print("You can now upload videos with:")
        print("python lofi upload videos/my_video.mp4 chill")
    else:
        print("\\n❌ Service account setup incomplete.")
        print("Please follow the setup instructions.")
    sys.exit(0 if success else 1)
'''
    
    test_script_path = Path(__file__).parent / "test_service_account.py"
    with open(test_script_path, 'w') as f:
        f.write(test_script_content)
    
    print(f"✅ Created test script: {test_script_path}")

def main():
    """Main setup function."""
    print_header()
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    
    if not service_account_file:
        print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE environment variable not set")
        print("Please set it in your .env file:")
        print("YOUTUBE_SERVICE_ACCOUNT_FILE=/path/to/service-account.json")
        return False
    
    print(f"📁 Service account file: {service_account_file}")
    
    # Check service account file
    service_account_data = check_service_account_file(service_account_file)
    if not service_account_data:
        return False
    
    service_account_email = service_account_data.get('client_email')
    
    # Create test script
    create_test_script()
    
    # Print instructions
    print_youtube_studio_instructions(service_account_email)
    
    # Ask if user wants to open YouTube Studio
    print("\n" + "="*50)
    try:
        response = input("🌐 Open YouTube Studio now? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            open_youtube_studio()
            print()
            print("📋 Service account email to copy:")
            print(f"   {service_account_email}")
            print()
            input("Press Enter after you've added the service account in YouTube Studio...")
            
            # Test the setup
            print("\n🧪 Testing service account setup...")
            os.system("python scripts/test_service_account.py")
        else:
            print("\nManual setup required. Follow the instructions above.")
    except (EOFError, KeyboardInterrupt):
        print("\nSetup instructions provided above.")
    
    # Print troubleshooting
    print_troubleshooting()
    
    return True

if __name__ == "__main__":
    main()
