#!/usr/bin/env python3
"""
Set up YouTube OAuth2 authentication for lo-fi video uploads.
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from youtube.uploader import YouTubeUploader


def validate_oauth_credentials_file(file_path: str) -> bool:
    """Validate OAuth2 credentials JSON file."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ OAuth2 credentials file not found: {file_path}")
            return False
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        # Check for OAuth2 structure
        if 'installed' not in data and 'web' not in data:
            print(f"❌ Invalid OAuth2 credentials format")
            print("Expected 'installed' or 'web' key in JSON")
            return False
        
        oauth_data = data.get('installed', data.get('web', {}))
        required_fields = ['client_id', 'client_secret', 'auth_uri', 'token_uri']
        missing_fields = [field for field in required_fields if field not in oauth_data]
        
        if missing_fields:
            print(f"❌ Missing required OAuth2 fields: {', '.join(missing_fields)}")
            return False
        
        print(f"✅ OAuth2 credentials file is valid")
        print(f"   🆔 Client ID: {oauth_data.get('client_id')[:20]}...")
        print(f"   🏗️ Project ID: {oauth_data.get('project_id', 'Not specified')}")
        return True
        
    except Exception as e:
        print(f"❌ Error reading OAuth2 credentials file: {e}")
        return False


async def test_oauth_authentication(oauth_credentials_file: str) -> bool:
    """Test OAuth2 authentication with YouTube API."""
    try:
        print("\n🔐 Testing OAuth2 authentication...")
        print("📝 Note: This will open a browser window for authentication")
        
        uploader = YouTubeUploader(oauth_credentials_file, auth_type="oauth2")
        auth_success = await uploader.authenticate()
        
        if auth_success:
            print("✅ OAuth2 authentication successful")
            
            # Try to access channel information
            try:
                response = uploader.youtube_service.channels().list(
                    part='snippet,contentDetails,statistics',
                    mine=True
                ).execute()
                
                if response.get('items'):
                    channel = response['items'][0]
                    print(f"✅ Successfully accessed YouTube channel:")
                    print(f"   📺 Channel: {channel['snippet']['title']}")
                    print(f"   🆔 Channel ID: {channel['id']}")
                    
                    # Check statistics if available
                    if 'statistics' in channel:
                        stats = channel['statistics']
                        print(f"   👥 Subscribers: {stats.get('subscriberCount', 'Hidden')}")
                        print(f"   🎥 Videos: {stats.get('videoCount', '0')}")
                        print(f"   👀 Views: {stats.get('viewCount', '0')}")
                    
                    return True
                else:
                    print("❌ No YouTube channel found")
                    print("   Make sure you have a YouTube channel associated with your Google account")
                    return False
                    
            except Exception as e:
                print(f"❌ Failed to access YouTube channel: {e}")
                return False
        else:
            print("❌ OAuth2 authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False


def print_oauth_setup_instructions():
    """Print instructions for setting up OAuth2 credentials."""
    print("\n" + "="*60)
    print("🔧 HOW TO SET UP OAUTH2 CREDENTIALS")
    print("="*60)
    print()
    print("If you don't have OAuth2 credentials yet, follow these steps:")
    print()
    print("1. 🌐 Go to Google Cloud Console:")
    print("   https://console.cloud.google.com/")
    print()
    print("2. 📁 Select or create a project")
    print()
    print("3. 🔌 Enable YouTube Data API v3:")
    print("   - Go to 'APIs & Services' > 'Library'")
    print("   - Search for 'YouTube Data API v3'")
    print("   - Click 'Enable'")
    print()
    print("4. 🔑 Create OAuth2 credentials:")
    print("   - Go to 'APIs & Services' > 'Credentials'")
    print("   - Click 'Create Credentials' > 'OAuth client ID'")
    print("   - Choose 'Desktop application'")
    print("   - Give it a name (e.g., 'LoFi Video Uploader')")
    print("   - Click 'Create'")
    print()
    print("5. 📥 Download credentials:")
    print("   - Click the download button next to your OAuth client")
    print("   - Save as 'youtube-oauth-account.json' in your project root")
    print()
    print("6. 🔄 Run this script again:")
    print("   python scripts/setup_youtube_oauth.py")


def create_env_template(oauth_credentials_file: str):
    """Create .env template with OAuth2 configuration."""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    
    # Read existing .env if it exists
    existing_content = ""
    if env_file.exists():
        with open(env_file, 'r') as f:
            existing_content = f.read()
    
    # Check if OAuth config already exists
    if "YOUTUBE_OAUTH_CREDENTIALS_FILE" in existing_content:
        print("✅ OAuth2 configuration already exists in .env file")
        return
    
    # YouTube OAuth2 configuration template
    oauth_config = f"""
# YouTube Upload Configuration (OAuth2)
YOUTUBE_OAUTH_CREDENTIALS_FILE={oauth_credentials_file}
"""
    
    # Append to .env file
    with open(env_file, 'a') as f:
        f.write(oauth_config)
    
    print(f"✅ Updated .env file: {env_file}")


async def main():
    """Main setup function."""
    print("🚀 Lo-Fi Channel YouTube OAuth2 Setup")
    print("="*40)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    oauth_credentials_file = os.getenv("YOUTUBE_OAUTH_CREDENTIALS_FILE")
    
    if not oauth_credentials_file:
        print("⚠️ YOUTUBE_OAUTH_CREDENTIALS_FILE environment variable not set")
        print()
        
        # Check if file exists in project root
        project_root = Path(__file__).parent.parent
        default_file = project_root / "youtube-oauth-account.json"
        
        if default_file.exists():
            print(f"📁 Found OAuth2 credentials file: {default_file}")
            oauth_credentials_file = str(default_file)
        else:
            print_oauth_setup_instructions()
            
            # Ask user for file path
            file_path = input("\nEnter path to your OAuth2 credentials JSON file (or press Enter to skip): ").strip()
            if file_path:
                oauth_credentials_file = file_path
            else:
                print("Setup incomplete. Please set up OAuth2 credentials and run again.")
                sys.exit(1)
    
    print(f"📁 OAuth2 credentials file: {oauth_credentials_file}")
    
    # Validate credentials file
    if not validate_oauth_credentials_file(oauth_credentials_file):
        return False
    
    # Test authentication
    auth_success = await test_oauth_authentication(oauth_credentials_file)
    
    if auth_success:
        # Create/update .env file
        create_env_template(oauth_credentials_file)
        
        print("\n✅ OAuth2 setup completed successfully!")
        print("\nNext steps:")
        print("1. Your OAuth2 token has been saved for future use")
        print("2. Test upload with: python lofi upload videos/my_video.mp4 chill")
        print("3. Check status with: python lofi status")
        
        # Show auth info
        uploader = YouTubeUploader(oauth_credentials_file, auth_type="oauth2")
        auth_info = uploader.get_auth_info()
        if auth_info.get("token_file"):
            print(f"\n📄 OAuth2 token saved to: {auth_info['token_file']}")
        
        return True
    else:
        print("\n❌ OAuth2 setup failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
