"""
YouTube upload pipeline for lo-fi videos.
"""

import os
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
from zoneinfo import ZoneInfo

# Always use absolute imports
from database.manager import DatabaseManager
from database.models import UploadedVideo, UploadStatus
from youtube.uploader import YouTubeUploader
from youtube.metadata import generate_video_metadata, validate_content_guidelines
from notifications.slack import SlackNotifier
from scheduler.scheduler import VideoScheduler
from error_tracking import (
    capture_exception, capture_message, track_performance,
    set_operation_context, add_breadcrumb
)
from music_sources.base import Track, LoFiStyle

logger = logging.getLogger(__name__)


class UploadPipeline:
    """Main upload pipeline for YouTube videos."""
    
    def __init__(self,
                 credentials_file: str,
                 database_path: str = "lofi_channel.db",
                 slack_webhook_url: Optional[str] = None,
                 timezone: str = "UTC",
                 auth_type: str = "auto"):
        """
        Initialize upload pipeline.

        Args:
            credentials_file: Path to YouTube credentials JSON file (OAuth2 or service account)
            database_path: Path to SQLite database
            slack_webhook_url: Slack webhook URL for notifications
            timezone: Timezone for scheduling
            auth_type: Authentication type ("oauth2", "service_account", or "auto")
        """
        self.db_manager = DatabaseManager(database_path)
        self.youtube_uploader = YouTubeUploader(credentials_file, auth_type=auth_type)
        self.slack_notifier = SlackNotifier(slack_webhook_url) if slack_webhook_url else None
        self.scheduler = VideoScheduler(
            self.db_manager,
            self.youtube_uploader,
            self.slack_notifier,
            timezone
        )
        self.timezone = ZoneInfo(timezone)
    
    async def initialize(self) -> bool:
        """Initialize all components."""
        try:
            # Initialize database
            await self.db_manager.initialize()
            
            # Authenticate with YouTube
            auth_success = await self.youtube_uploader.authenticate()
            if not auth_success:
                logger.error("❌ YouTube authentication failed")
                return False
            
            # Test Slack connection if configured
            if self.slack_notifier:
                await self.slack_notifier.test_connection()
            
            logger.info("✅ Upload pipeline initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize upload pipeline: {e}")
            capture_exception(e, context={"component": "upload_pipeline_init"})
            return False
    
    @track_performance("upload_video")
    async def upload_video(self,
                          video_path: Path,
                          tracks: List[Track],
                          style: LoFiStyle,
                          duration: float,
                          scheduled_publish_time: Optional[datetime] = None,
                          force_upload: bool = False) -> Optional[str]:
        """
        Upload a video to YouTube as a draft.

        Args:
            video_path: Path to video file
            tracks: List of tracks used in the video
            style: Lo-fi style
            duration: Video duration in seconds
            scheduled_publish_time: When to publish the video (optional)
            force_upload: Force upload even if duplicate detected

        Returns:
            YouTube video ID if successful, None otherwise
        """
        set_operation_context("upload_video", video_title=str(video_path.name))
        add_breadcrumb(f"Starting upload for {video_path.name}", category="upload")
        
        try:
            # Calculate file hash for database storage
            file_hash = self.db_manager.calculate_file_hash(video_path)

            # Check for duplicates (unless force upload is enabled)
            if not force_upload:
                existing_video = await self.db_manager.get_video_by_hash(file_hash)

                if existing_video:
                    logger.warning(f"⚠️ Duplicate video detected: {video_path}")
                    if self.slack_notifier:
                        await self.slack_notifier.notify_duplicate_detected(
                            str(video_path.name),
                            video_path,
                            existing_video.youtube_video_id
                        )
                    return existing_video.youtube_video_id
            else:
                logger.info(f"🔄 Force upload enabled, skipping duplicate check for: {video_path}")
            
            # Generate metadata
            metadata = generate_video_metadata(style, tracks, duration, video_path)
            add_breadcrumb(f"Generated metadata: {metadata.title}", category="metadata")
            
            # Validate content guidelines
            guidelines = await self.db_manager.get_content_guidelines()
            violations = validate_content_guidelines(metadata, guidelines)
            
            if violations:
                logger.error(f"❌ Content guideline violations: {violations}")
                if self.slack_notifier:
                    await self.slack_notifier.notify_content_violation(
                        metadata.title, violations
                    )
                capture_message(
                    f"Content guideline violations for {metadata.title}",
                    level="warning",
                    context={"violations": violations}
                )
                return None
            
            # Create database record
            video_record = UploadedVideo(
                video_path=str(video_path),
                file_hash=file_hash,
                title=metadata.title,
                description=metadata.description,
                style=style.value,
                duration=int(duration),
                upload_status=UploadStatus.PENDING,
                scheduled_publish_time=scheduled_publish_time
            )
            
            video_id = await self.db_manager.add_video(video_record)
            add_breadcrumb(f"Created database record: {video_id}", category="database")
            
            # Notify upload started
            if self.slack_notifier:
                await self.slack_notifier.notify_upload_started(metadata.title, video_path)
            
            # Update status to uploading
            await self.db_manager.update_video_status(video_id, UploadStatus.UPLOADING)
            
            # Upload to YouTube
            youtube_video_id = await self.youtube_uploader.upload_video(
                video_path, metadata, notify_subscribers=False
            )
            
            if youtube_video_id:
                # Update database with YouTube video ID
                await self.db_manager.update_video_status(
                    video_id, 
                    UploadStatus.UPLOADED, 
                    youtube_video_id
                )
                
                # Schedule for publishing if requested
                if scheduled_publish_time:
                    await self.scheduler.schedule_video_publication(
                        video_id, scheduled_publish_time
                    )
                
                # Notify upload completed
                if self.slack_notifier:
                    video_url = f"https://www.youtube.com/watch?v={youtube_video_id}"
                    await self.slack_notifier.notify_upload_completed(
                        metadata.title, youtube_video_id, video_url
                    )
                
                logger.info(f"✅ Video uploaded successfully: {youtube_video_id}")
                add_breadcrumb(f"Upload completed: {youtube_video_id}", category="upload")
                
                return youtube_video_id
            else:
                # Upload failed
                await self.db_manager.update_video_status(video_id, UploadStatus.FAILED)
                
                error_msg = "YouTube upload failed"
                if self.slack_notifier:
                    await self.slack_notifier.notify_upload_failed(
                        metadata.title, error_msg, video_path
                    )
                
                logger.error(f"❌ {error_msg}: {video_path}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Upload pipeline error: {e}")
            capture_exception(e, context={
                "video_path": str(video_path),
                "style": style.value,
                "duration": duration
            })
            
            if self.slack_notifier:
                await self.slack_notifier.notify_upload_failed(
                    str(video_path.name), str(e), video_path
                )
            
            return None
    
    async def publish_video_now(self, video_id: int) -> bool:
        """
        Immediately publish a video that's currently a draft.
        
        Args:
            video_id: Database video ID
            
        Returns:
            True if published successfully, False otherwise
        """
        set_operation_context("publish_video_now")
        
        try:
            # Get video from database
            videos = await self.db_manager.get_videos_by_status(UploadStatus.UPLOADED)
            video = next((v for v in videos if v.id == video_id), None)
            
            if not video:
                logger.error(f"❌ Video {video_id} not found or not in uploaded status")
                return False
            
            if not video.youtube_video_id:
                logger.error(f"❌ Video {video_id} has no YouTube ID")
                return False
            
            # Update video status to public on YouTube
            success = await self.youtube_uploader.update_video_status(
                video.youtube_video_id,
                privacy_status="public"
            )
            
            if success:
                # Mark as published in database
                await self.db_manager.mark_published(video_id)
                
                # Notify via Slack
                if self.slack_notifier:
                    video_url = f"https://www.youtube.com/watch?v={video.youtube_video_id}"
                    await self.slack_notifier.notify_published(
                        video.title, video.youtube_video_id, video_url
                    )
                
                logger.info(f"✅ Video published: {video.title}")
                return True
            else:
                logger.error(f"❌ Failed to publish video {video_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error publishing video {video_id}: {e}")
            capture_exception(e, context={"video_id": video_id})
            return False
    
    async def get_upload_status(self) -> Dict[str, Any]:
        """Get upload pipeline status."""
        try:
            status = {
                "database_initialized": self.db_manager._initialized,
                "youtube_authenticated": self.youtube_uploader._authenticated,
                "slack_enabled": self.slack_notifier.enabled if self.slack_notifier else False,
                "videos_by_status": {}
            }
            
            # Get video counts by status
            for upload_status in UploadStatus:
                videos = await self.db_manager.get_videos_by_status(upload_status)
                status["videos_by_status"][upload_status.value] = len(videos)
            
            # Get next scheduled videos
            status["next_scheduled"] = await self.scheduler.get_next_scheduled_videos(5)
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Error getting upload status: {e}")
            capture_exception(e)
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            await self.db_manager.close()
            self.scheduler.stop_scheduler()
            logger.info("✅ Upload pipeline cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            capture_exception(e)
