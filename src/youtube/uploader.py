"""
YouTube uploader service for lo-fi videos.
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime

from google.auth.transport.requests import Request
from google.oauth2 import service_account
from google_auth_oauthlib.flow import InstalledAppFlow
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload

# Always use absolute imports
from youtube.metadata import VideoMetadata

logger = logging.getLogger(__name__)


class YouTubeUploader:
    """Handles YouTube video uploads using OAuth2 or service account authentication."""

    # YouTube API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/youtube.upload',
        'https://www.googleapis.com/auth/youtube',
        'https://www.googleapis.com/auth/youtube.readonly'
    ]

    def __init__(self, credentials_file: str, channel_id: Optional[str] = None, auth_type: str = "auto"):
        """
        Initialize YouTube uploader.

        Args:
            credentials_file: Path to credentials JSON file (OAuth2 or service account)
            channel_id: YouTube channel ID (optional, for validation)
            auth_type: Authentication type ("oauth2", "service_account", or "auto")
        """
        self.credentials_file = Path(credentials_file)
        self.channel_id = channel_id
        self.auth_type = auth_type
        self.youtube_service = None
        self._authenticated = False
        self._credentials = None
        self._token_file = None

        # Determine auth type if auto
        if auth_type == "auto":
            self.auth_type = self._detect_auth_type()

    def _detect_auth_type(self) -> str:
        """Detect authentication type from credentials file."""
        try:
            with open(self.credentials_file, 'r') as f:
                data = json.load(f)

            if 'type' in data and data['type'] == 'service_account':
                return "service_account"
            elif 'installed' in data or 'web' in data:
                return "oauth2"
            else:
                logger.warning("Unknown credentials format, defaulting to OAuth2")
                return "oauth2"

        except Exception as e:
            logger.error(f"Failed to detect auth type: {e}")
            return "oauth2"
    
    async def authenticate(self) -> bool:
        """Authenticate with YouTube API using OAuth2 or service account."""
        try:
            if not self.credentials_file.exists():
                logger.error(f"❌ Credentials file not found: {self.credentials_file}")
                return False

            if self.auth_type == "service_account":
                return await self._authenticate_service_account()
            elif self.auth_type == "oauth2":
                return await self._authenticate_oauth2()
            else:
                logger.error(f"❌ Unknown authentication type: {self.auth_type}")
                return False

        except Exception as e:
            logger.error(f"❌ YouTube authentication failed: {e}")
            return False

    async def _authenticate_service_account(self) -> bool:
        """Authenticate using service account."""
        try:
            # Load service account credentials
            credentials = service_account.Credentials.from_service_account_file(
                str(self.credentials_file),
                scopes=self.SCOPES
            )

            # Build YouTube service
            self.youtube_service = build('youtube', 'v3', credentials=credentials)
            self._credentials = credentials
            self._authenticated = True

            logger.info("✅ YouTube API authentication successful (Service Account)")
            return True

        except Exception as e:
            logger.error(f"❌ Service account authentication failed: {e}")
            return False

    async def _authenticate_oauth2(self) -> bool:
        """Authenticate using OAuth2."""
        try:
            # Set up token file path
            self._token_file = self.credentials_file.parent / "youtube_token.json"

            credentials = None

            # Load existing token if available
            if self._token_file.exists():
                try:
                    import json
                    with open(self._token_file, 'r') as token_file:
                        token_data = json.load(token_file)

                    credentials = Credentials(
                        token=token_data.get('token'),
                        refresh_token=token_data.get('refresh_token'),
                        token_uri=token_data.get('token_uri'),
                        client_id=token_data.get('client_id'),
                        client_secret=token_data.get('client_secret'),
                        scopes=token_data.get('scopes')
                    )
                    logger.info("📄 Loaded existing OAuth2 token")
                except Exception as e:
                    logger.warning(f"Failed to load existing token: {e}")

            # If no valid credentials, run OAuth2 flow
            if not credentials or not credentials.valid:
                if credentials and credentials.expired and credentials.refresh_token:
                    try:
                        credentials.refresh(Request())
                        logger.info("🔄 Refreshed OAuth2 token")
                    except Exception as e:
                        logger.warning(f"Failed to refresh token: {e}")
                        credentials = None

                if not credentials:
                    # Run OAuth2 flow
                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(self.credentials_file),
                        self.SCOPES
                    )

                    # Try different ports if 8080 is in use
                    ports_to_try = [8080, 8081, 8082, 8083, 8084]
                    credentials = None

                    for port in ports_to_try:
                        try:
                            # Use local server for OAuth2 callback
                            credentials = flow.run_local_server(
                                port=port,
                                prompt='consent',
                                authorization_prompt_message='Please visit this URL to authorize the application: {url}',
                                success_message='The auth flow is complete; you may close this window.'
                            )
                            logger.info(f"✅ Completed OAuth2 flow on port {port}")
                            break
                        except OSError as e:
                            if "Address already in use" in str(e):
                                logger.warning(f"Port {port} in use, trying next port...")
                                continue
                            else:
                                raise e

                    if not credentials:
                        raise Exception("Could not find an available port for OAuth2 callback")

                # Save token for future use
                with open(self._token_file, 'w') as token:
                    token.write(credentials.to_json())
                logger.info(f"💾 Saved OAuth2 token to {self._token_file}")

            # Build YouTube service
            self.youtube_service = build('youtube', 'v3', credentials=credentials)
            self._credentials = credentials
            self._authenticated = True

            logger.info("✅ YouTube API authentication successful (OAuth2)")
            return True

        except Exception as e:
            logger.error(f"❌ OAuth2 authentication failed: {e}")
            return False
    
    async def upload_video(
        self,
        video_path: Path,
        metadata: VideoMetadata,
        notify_subscribers: bool = False
    ) -> Optional[str]:
        """
        Upload video to YouTube as a draft.
        
        Args:
            video_path: Path to video file
            metadata: Video metadata
            notify_subscribers: Whether to notify subscribers (default: False for drafts)
            
        Returns:
            YouTube video ID if successful, None otherwise
        """
        if not self._authenticated:
            if not await self.authenticate():
                return None
        
        if not video_path.exists():
            logger.error(f"❌ Video file not found: {video_path}")
            return None
        
        try:
            # Prepare video metadata for upload
            body = {
                'snippet': {
                    'title': metadata.title,
                    'description': metadata.description,
                    'tags': metadata.tags,
                    'categoryId': metadata.category_id
                },
                'status': {
                    'privacyStatus': metadata.privacy_status,
                    'selfDeclaredMadeForKids': False,
                    'notifySubscribers': notify_subscribers
                }
            }
            
            # Create media upload object
            media = MediaFileUpload(
                str(video_path),
                chunksize=-1,  # Upload in single chunk
                resumable=True,
                mimetype='video/*'
            )
            
            logger.info(f"🚀 Starting upload: {metadata.title}")
            
            # Execute upload
            insert_request = self.youtube_service.videos().insert(
                part=','.join(body.keys()),
                body=body,
                media_body=media
            )
            
            response = None
            error = None
            retry = 0
            max_retries = 3
            
            while response is None and retry < max_retries:
                try:
                    status, response = insert_request.next_chunk()
                    if status:
                        logger.info(f"📤 Upload progress: {int(status.progress() * 100)}%")
                except HttpError as e:
                    if e.resp.status in [500, 502, 503, 504]:
                        # Retriable error
                        retry += 1
                        logger.warning(f"⚠️ Retriable error {e.resp.status}, retry {retry}/{max_retries}")
                        continue
                    else:
                        logger.error(f"❌ Non-retriable HTTP error: {e}")
                        raise
                except Exception as e:
                    logger.error(f"❌ Upload error: {e}")
                    raise
            
            if response is not None:
                video_id = response['id']
                logger.info(f"✅ Video uploaded successfully: {video_id}")
                logger.info(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")
                return video_id
            else:
                logger.error("❌ Upload failed after all retries")
                return None
                
        except HttpError as e:
            logger.error(f"❌ YouTube API error: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Upload failed: {e}")
            return None
    
    async def update_video_status(
        self,
        video_id: str,
        privacy_status: str = "public",
        publish_at: Optional[datetime] = None
    ) -> bool:
        """
        Update video privacy status or schedule publication.
        
        Args:
            video_id: YouTube video ID
            privacy_status: New privacy status ('public', 'private', 'unlisted')
            publish_at: Schedule publication time (for 'public' status)
            
        Returns:
            True if successful, False otherwise
        """
        if not self._authenticated:
            if not await self.authenticate():
                return False
        
        try:
            body = {
                'id': video_id,
                'status': {
                    'privacyStatus': privacy_status,
                    'selfDeclaredMadeForKids': False
                }
            }
            
            # Add scheduled publish time if provided
            if publish_at and privacy_status == 'public':
                body['status']['publishAt'] = publish_at.isoformat() + 'Z'
            
            response = self.youtube_service.videos().update(
                part='status',
                body=body
            ).execute()
            
            logger.info(f"✅ Video {video_id} status updated to {privacy_status}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ Failed to update video status: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error updating video status: {e}")
            return False
    
    async def get_video_info(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video information from YouTube."""
        if not self._authenticated:
            if not await self.authenticate():
                return None
        
        try:
            response = self.youtube_service.videos().list(
                part='snippet,status,statistics',
                id=video_id
            ).execute()
            
            if response['items']:
                return response['items'][0]
            else:
                logger.warning(f"⚠️ Video not found: {video_id}")
                return None
                
        except HttpError as e:
            logger.error(f"❌ Failed to get video info: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error getting video info: {e}")
            return None
    
    async def delete_video(self, video_id: str) -> bool:
        """Delete a video from YouTube."""
        if not self._authenticated:
            if not await self.authenticate():
                return False
        
        try:
            self.youtube_service.videos().delete(id=video_id).execute()
            logger.info(f"✅ Video deleted: {video_id}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ Failed to delete video: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error deleting video: {e}")
            return False
    
    def validate_setup(self) -> Dict[str, bool]:
        """Validate YouTube uploader setup."""
        checks = {
            "credentials_file_exists": self.credentials_file.exists(),
            "credentials_file_readable": False,
            "credentials_valid": False,
            "auth_type_detected": False
        }

        if checks["credentials_file_exists"]:
            try:
                with open(self.credentials_file, 'r') as f:
                    data = json.load(f)
                    checks["credentials_file_readable"] = True

                    # Check credentials based on type
                    if self.auth_type == "service_account":
                        required_fields = ['type', 'project_id', 'private_key', 'client_email']
                        if all(field in data for field in required_fields):
                            checks["credentials_valid"] = data.get('type') == 'service_account'
                    elif self.auth_type == "oauth2":
                        # Check for OAuth2 credentials
                        if 'installed' in data or 'web' in data:
                            oauth_data = data.get('installed', data.get('web', {}))
                            required_fields = ['client_id', 'client_secret', 'auth_uri', 'token_uri']
                            checks["credentials_valid"] = all(field in oauth_data for field in required_fields)

                    checks["auth_type_detected"] = self.auth_type in ["oauth2", "service_account"]

            except Exception as e:
                logger.error(f"Error validating credentials file: {e}")

        return checks

    def get_auth_info(self) -> Dict[str, Any]:
        """Get authentication information."""
        info = {
            "auth_type": self.auth_type,
            "credentials_file": str(self.credentials_file),
            "authenticated": self._authenticated,
            "token_file": str(self._token_file) if self._token_file else None
        }

        if self.auth_type == "oauth2" and self._token_file and self._token_file.exists():
            info["token_exists"] = True

        return info
